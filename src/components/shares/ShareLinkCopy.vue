<script setup lang="ts">
	import { ref } from "vue";
	import { useI18n } from "vue-i18n";
	import { Success } from "@/utils/notify";
	import { ClipboardIcon, CheckIcon } from "@heroicons/vue/24/outline";

	const { t } = useI18n();

	const props = defineProps<{
		shareUuid: string;
	}>();

	const copied = ref(false);

	const getShareUrl = () => {
		if (typeof window !== "undefined" && window.location) {
			return `${window.location.origin}/dashboard/shared/${props.shareUuid}`;
		}
		return `/dashboard/shared/${props.shareUuid}`;
	};

	const shareUrl = getShareUrl();

	const copyToClipboard = async () => {
		try {
			await navigator.clipboard.writeText(shareUrl);
			copied.value = true;
			Success(t("success"), t("shares.link_copied"));

			// Reset copied state after 2 seconds
			setTimeout(() => {
				copied.value = false;
			}, 2000);
		} catch (err) {
			// Fallback for browsers that don't support clipboard API
			const textArea = document.createElement("textarea");
			textArea.value = shareUrl;
			document.body.appendChild(textArea);
			textArea.focus();
			textArea.select();
			document.execCommand("copy");
			document.body.removeChild(textArea);

			copied.value = true;
			Success(t("success"), t("shares.link_copied"));

			setTimeout(() => {
				copied.value = false;
			}, 2000);
		}
	};
</script>

<template>
	<div class="dropdown dropdown-left">
		<div tabindex="0" role="button" class="btn btn-ghost btn-sm">
			<component :is="copied ? CheckIcon : ClipboardIcon" class="w-4 h-4" />
			{{ copied ? t("shares.copied") : t("shares.copy_link") }}
		</div>
		<div
			tabindex="0"
			class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-80"
		>
			<div class="p-2">
				<p class="text-sm text-base-content/70 mb-2">{{ t("shares.share_link") }}:</p>
				<div class="flex items-center space-x-2">
					<input
						type="text"
						:value="shareUrl"
						readonly
						class="input input-bordered input-sm flex-1 font-mono text-xs"
					/>
					<button
						class="btn btn-primary btn-sm"
						@click="copyToClipboard"
						:class="{ 'btn-success': copied }"
					>
						<component :is="copied ? CheckIcon : ClipboardIcon" class="w-4 h-4" />
					</button>
				</div>
			</div>
		</div>
	</div>
</template>
